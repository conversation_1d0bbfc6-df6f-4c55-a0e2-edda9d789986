// TypeBox schema for budgets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'
import { LastSyncSchema } from '../care-accounts/utils/schemas.js'

export const budgetsSchema = Type.Object({
  _id: ObjectIdSchema(),
  amount: Type.Optional(Type.Number()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  assigned_amount: Type.Optional(Type.Number()),
  assigned_recurs: Type.Optional(Type.Number()),
  expenses: Type.Optional(Type.Array(ObjectIdSchema())),
  careAccount: Type.Optional(ObjectIdSchema()),
  category: Type.Optional(Type.String()),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  moov_id: Type.String(),
  connect_id: Type.Optional(Type.String()),
  lastInc: Type.Optional(Type.String()),
  lastSync: LastSyncSchema,
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  name: Type.String(),
  owner: ObjectIdSchema(),
  parent: Type.Optional(ObjectIdSchema()),
  priorAuth: Type.Optional(Type.Object({
    min: Type.Optional(Type.Number()),
    max: Type.Optional(Type.Number()),
  })),
  recurs: Type.Optional(Type.Number()),
  runSync: Type.Optional(Type.String()),
  spent: Type.Optional(Type.Number()),
  spent_pending: Type.Optional(Type.Number()),
  spent_pending_sub: Type.Optional(Type.Number()),
  spent_sub: Type.Optional(Type.Number()),
  syncHistory: Type.Optional(Type.Array(LastSyncSchema)),
  rampSpendProgram: Type.Optional(Type.String()),
  priorMonths: Type.Optional(Type.Array(Type.Object({
    amount: Type.Optional(Type.Number()),
    recurs: Type.Optional(Type.Number()),
    parent: Type.Optional(ObjectIdSchema()),
    careAccount: Type.Optional(ObjectIdSchema()),
    spent_sub: Type.Optional(Type.Number()),
    spent_pending: Type.Optional(Type.Number()),
    spent_pending_sub: Type.Optional(Type.Number()),
    spent: Type.Optional(Type.Number()),
  }))),
  ...commonFields
}, { $id: 'Budgets', additionalProperties: false })

export type Budgets = Static<typeof budgetsSchema>
export const budgetsValidator = getValidator(budgetsSchema, dataValidator)
export const budgetsResolver = resolve<Budgets, HookContext>({})
export const budgetsExternalResolver = resolve<Budgets, HookContext>({})

export const budgetsDataSchema = Type.Object({
  ...Type.Omit(budgetsSchema, ['_id']).properties
}, { additionalProperties: false })

export type BudgetsData = Static<typeof budgetsDataSchema>
export const budgetsDataValidator = getValidator(budgetsDataSchema, dataValidator)
export const budgetsDataResolver = resolve<BudgetsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const budgetsQueryProperties = Type.Pick(budgetsSchema, ['_id', 'approvers', 'expenses', 'careAccount', 'children', 'by', 'managers', 'members', 'owner', 'parent', 'parent', 'careAccount'])

export const budgetsPatchSchema = commonPatch(budgetsSchema, [], budgetsQueryProperties)
export type BudgetsPatch = Static<typeof budgetsPatchSchema>
export const budgetsPatchValidator = getValidator(budgetsPatchSchema, dataValidator)
export const budgetsPatchResolver = resolve<BudgetsPatch, HookContext>({})
export const budgetsQuerySchema = queryWrapper(budgetsQueryProperties)
export type BudgetsQuery = Static<typeof budgetsQuerySchema>
export const budgetsQueryValidator = getValidator(budgetsQuerySchema, queryValidator)
export const budgetsQueryResolver = resolve<BudgetsQuery, HookContext>({})
